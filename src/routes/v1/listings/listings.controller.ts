import type { App<PERSON>out<PERSON><PERSON><PERSON><PERSON> } from "@/lib/types";
import { HTTPException } from "hono/http-exception";
import { createErrorResponse } from "@/middlewares/validation";
import { getCurrentUser, getCurrentOrganization } from "@/middlewares/auth";
import { ListingDataService } from "./services/ListingDataService";
import { ListingQueryService } from "./services/ListingQueryService";
import { ListingValidationService } from "./services/ListingValidationService";
import { ListingBusinessService } from "./services/ListingBusinessService";
import { ListingsService } from "./listings.service"; // Keep for backward compatibility during transition
import type {
  listListingsRoute,
  getListingRoute,
  createListingRoute,
  saveDraftListingRoute,
  updateDraftListingRoute,
  updateListingRoute,
  deleteListingRoute,
  bulkCreateListingsRoute,
  bulkCreateListingsCsvRoute,
  getListingStatusHistoryRoute,
  getListingNotesRoute,
  createListingNoteRoute,
  updateListingNoteRoute,
  deleteListingNoteRoute,
} from "./listings.routes";

export const listListings: AppRouteHandler<typeof listListingsRoute> = async (c) => {
  try {
    // 1. Extract and validate request data
    const user = getCurrentUser(c);
    const organization = getCurrentOrganization(c);
    const query = c.req.valid("query");

    // 2. Build filters object
    const filters = {
      page: query.page,
      limit: query.limit,
      status: query.status,
      industry: query.industry,
      assignedTo: query.assignedTo,
      minPrice: query.minPrice,
      maxPrice: query.maxPrice,
      location: query.location,
      sortBy: query.sortBy,
      sortOrder: query.sortOrder,
      search: query.search,
    };

    // 3. Execute query through service
    const result = await ListingQueryService.getListings(filters, organization.id);

    // 4. Format and return response
    return c.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error('Error listing listings:', error);

    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'LISTING_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }

    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to retrieve listings',
      c.req.path
    ), 500);
  }
};

export const getListing: AppRouteHandler<typeof getListingRoute> = async (c) => {
  try {
    // 1. Extract and validate request data
    const user = getCurrentUser(c);
    const organization = getCurrentOrganization(c);
    const { listingId } = c.req.valid("param");
    const { includeDetails } = c.req.valid("query");

    // 2. Retrieve listing data
    const listing = await ListingDataService.getById(
      listingId,
      organization.id,
      includeDetails === 'true'
    );

    // 3. Check if listing exists
    if (!listing) {
      throw new HTTPException(404, { message: "Listing not found" });
    }

    // 4. Return formatted response
    return c.json({
      success: true,
      data: listing,
    });
  } catch (error) {
    console.error('Error getting listing:', error);

    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'LISTING_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }

    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to retrieve listing',
      c.req.path
    ), 500);
  }
};

export const createListing: AppRouteHandler<typeof createListingRoute> = async (c) => {
  try {
    // 1. Extract and validate request data
    const user = getCurrentUser(c);
    const organization = getCurrentOrganization(c);
    const body = c.req.valid("json");

    // 2. Prepare listing data with system fields
    const listingData = {
      ...body,
      organizationId: organization.id,
      createdBy: user.id,
    };

    // 3. Validate listing data
    const validation = ListingValidationService.validateCreateListing(listingData);
    if (!validation.isValid) {
      return c.json(createErrorResponse(
        'VALIDATION_ERROR',
        `Validation failed: ${validation.errors.map(e => e.message).join(', ')}`,
        c.req.path
      ), 400);
    }

    // 4. Create the listing
    const listing = await ListingDataService.create(listingData);

    // 5. Record initial status in history
    await ListingBusinessService.recordStatusChange(listing.id, {
      status: 'active',
      reason: 'Initial listing creation',
      changedBy: user.id,
      organizationId: organization.id,
    });

    // 6. Return formatted response
    return c.json({
      success: true,
      data: listing,
    }, 201);
  } catch (error) {
    console.error('Error creating listing:', error);

    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'CREATION_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }

    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to create listing',
      c.req.path
    ), 500);
  }
};

export const saveDraftListing: AppRouteHandler<typeof saveDraftListingRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const organization = getCurrentOrganization(c);
    const body = c.req.valid("json");

    const draftData = {
      ...body,
      organizationId: organization.id,
      createdBy: user.id,
    };

    const listing = await ListingsService.saveDraftListing(draftData);

    return c.json({
      success: true,
      data: listing,
    }, 201);
  } catch (error) {
    console.error('Error saving draft listing:', error);

    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'DRAFT_CREATION_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }

    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to save draft listing',
      c.req.path
    ), 500);
  }
};

export const updateDraftListing: AppRouteHandler<typeof updateDraftListingRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const organization = getCurrentOrganization(c);
    const { listingId } = c.req.valid("param");
    const body = c.req.valid("json");

    const draftData = {
      ...body,
      organizationId: organization.id,
      createdBy: user.id,
    };

    const listing = await ListingsService.updateDraftListing(listingId, draftData, organization.id);

    return c.json({
      success: true,
      data: listing,
    });
  } catch (error) {
    console.error('Error updating draft listing:', error);

    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'DRAFT_UPDATE_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }

    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to update draft listing',
      c.req.path
    ), 500);
  }
};

export const updateListing: AppRouteHandler<typeof updateListingRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const organization = getCurrentOrganization(c);
    const { listingId } = c.req.valid("param");
    const body = c.req.valid("json");

    // Check if this is a status update by looking for status field
    const isStatusUpdate = body.status !== undefined;

    let result;
    if (isStatusUpdate) {
      // Handle both listing update and status tracking
      result = await ListingsService.updateListingWithStatus(listingId, body, organization.id, user.id);
    } else {
      // Handle regular listing update
      result = await ListingsService.updateListing(listingId, body, organization.id);
    }

    return c.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error updating listing:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'UPDATE_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to update listing',
      c.req.path
    ), 500);
  }
};

export const deleteListing: AppRouteHandler<typeof deleteListingRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const organization = getCurrentOrganization(c);
    const { listingId } = c.req.valid("param");

    await ListingsService.deleteListing(listingId, organization.id);

    return c.json({
      success: true,
      message: 'Listing deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting listing:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'DELETE_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to delete listing',
      c.req.path
    ), 500);
  }
};

export const bulkCreateListingsCsv: AppRouteHandler<typeof bulkCreateListingsCsvRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const organization = getCurrentOrganization(c);

    // Parse multipart form data
    const body = await c.req.parseBody();
    const file = body.file as File;

    if (!file) {
      return c.json(createErrorResponse(
        'FILE_REQUIRED',
        'CSV file is required',
        c.req.path
      ), 400);
    }

    // Validate file type
    if (!file.type.includes('csv') && !file.name.endsWith('.csv')) {
      return c.json(createErrorResponse(
        'INVALID_FILE_TYPE',
        'File must be a CSV file',
        c.req.path
      ), 400);
    }

    // Validate file size (50MB limit)
    const MAX_FILE_SIZE = 50 * 1024 * 1024;
    if (file.size > MAX_FILE_SIZE) {
      return c.json(createErrorResponse(
        'FILE_TOO_LARGE',
        'File size exceeds maximum limit of 50MB',
        c.req.path
      ), 413);
    }

    // Process CSV in memory and save valid records to database
    const result = await ListingsService.bulkCreateListingsFromCsv(file, organization.id, user.id);

    return c.json({
      success: true,
      data: result,
    }, 201);
      } catch (error) {
    console.error('Error processing CSV bulk import:', error);

    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'CSV_IMPORT_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }

    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to process CSV import',
      c.req.path
    ), 500);
  }
};

export const getListingStatusHistory: AppRouteHandler<typeof getListingStatusHistoryRoute> = async (c) => {
  try {
    // 1. Extract and validate request data
    const user = getCurrentUser(c);
    const organization = getCurrentOrganization(c);
    const { listingId } = c.req.valid("param");

    // 2. Verify listing exists and user has access
    const listing = await ListingDataService.getById(listingId, organization.id, false);
    if (!listing) {
      throw new HTTPException(404, { message: "Listing not found" });
    }

    // 3. Get status history
    const history = await ListingBusinessService.getStatusHistory(listingId, organization.id);

    // 4. Return formatted response
    return c.json({
      success: true,
      data: history,
    });
  } catch (error) {
    console.error('Error getting listing status history:', error);

    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'HISTORY_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }

    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to retrieve listing status history',
      c.req.path
    ), 500);
  }
};

// Listing Notes Controllers
export const getListingNotes: AppRouteHandler<typeof getListingNotesRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const organization = getCurrentOrganization(c);
    const { listingId } = c.req.valid("param");

    // Verify listing exists and user has access
    await ListingsService.getListingById(listingId, organization.id, false);

    const notes = await ListingsService.getListingNotes(listingId, organization.id);

    return c.json({
      success: true,
      data: notes,
    });
  } catch (error) {
    console.error('Error getting listing notes:', error);

    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'NOTES_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }

    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to retrieve listing notes',
      c.req.path
    ), 500);
  }
};

export const createListingNote: AppRouteHandler<typeof createListingNoteRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const organization = getCurrentOrganization(c);
    const { listingId } = c.req.valid("param");
    const body = c.req.valid("json");

    // Verify listing exists and user has access
    await ListingsService.getListingById(listingId, organization.id, false);

    const noteData = {
      listingId,
      organizationId: organization.id,
      createdBy: user.id,
      content: body.content,
      mentions: body.mentions,
      isPrivate: body.isPrivate,
    };

    const note = await ListingsService.createListingNote(noteData);

    return c.json({
      success: true,
      data: note,
    }, 201);
  } catch (error) {
    console.error('Error creating listing note:', error);

    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'NOTE_CREATION_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }

    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to create listing note',
      c.req.path
    ), 500);
  }
};

export const updateListingNote: AppRouteHandler<typeof updateListingNoteRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const organization = getCurrentOrganization(c);
    const { listingId, noteId } = c.req.valid("param");
    const body = c.req.valid("json");

    // Verify listing exists and user has access
    await ListingsService.getListingById(listingId, organization.id, false);

    const note = await ListingsService.updateListingNote(
      noteId,
      body,
      organization.id,
      user.id
    );

    return c.json({
      success: true,
      data: note,
    });
  } catch (error) {
    console.error('Error updating listing note:', error);

    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'NOTE_UPDATE_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }

    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to update listing note',
      c.req.path
    ), 500);
  }
};

export const deleteListingNote: AppRouteHandler<typeof deleteListingNoteRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const organization = getCurrentOrganization(c);
    const { listingId, noteId } = c.req.valid("param");

    // Verify listing exists and user has access
    await ListingsService.getListingById(listingId, organization.id, false);

    await ListingsService.deleteListingNote(noteId, organization.id, user.id);

    return c.json({
      success: true,
      message: 'Note deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting listing note:', error);

    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'NOTE_DELETE_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }

    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to delete listing note',
      c.req.path
    ), 500);
  }
};
